const mongoose = require("mongoose");
const AppointmentRequest = require("../models/adminSideSchema/dashboard/appointmentRequestSchema");
const Attachment = require("../models/appointmentSchema/attachmentSchema");
const Student = require("../models/appointmentSchema/studentSchema");

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/appointment-system", {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
};

// Migration script to link existing attachments to appointment requests
const migrateAttachments = async () => {
  try {
    console.log("🔄 Starting attachment migration...");

    // Get all attachments
    const attachments = await Attachment.find().populate("files.student");
    console.log(`Found ${attachments.length} attachment records`);

    // Get all appointment requests
    const appointmentRequests = await AppointmentRequest.find();
    console.log(`Found ${appointmentRequests.length} appointment request records`);

    let linkedCount = 0;

    for (const attachment of attachments) {
      if (!attachment.files || attachment.files.length === 0) continue;

      // Get the student from the first file (assuming all files in an attachment belong to the same student)
      const firstFile = attachment.files[0];
      if (!firstFile.student) continue;

      const student = await Student.findById(firstFile.student);
      if (!student || !student.transactionNumber) continue;

      // Find the corresponding appointment request
      const appointmentRequest = await AppointmentRequest.findOne({
        transactionNumber: student.transactionNumber,
      });

      if (appointmentRequest && !appointmentRequest.attachmentProof) {
        // Link the attachment to the appointment request
        appointmentRequest.attachmentProof = attachment._id;
        await appointmentRequest.save();

        console.log(
          `✅ Linked attachment ${attachment._id} to appointment request ${appointmentRequest.transactionNumber}`
        );
        linkedCount++;
      }
    }

    console.log(`🎉 Migration completed! Linked ${linkedCount} attachments to appointment requests.`);
  } catch (error) {
    console.error("❌ Migration error:", error);
  }
};

// Debug function to show current state
const debugCurrentState = async () => {
  try {
    console.log("\n📊 Current Database State:");
    
    const students = await Student.countDocuments();
    console.log(`Students: ${students}`);
    
    const attachments = await Attachment.countDocuments();
    console.log(`Attachments: ${attachments}`);
    
    const appointmentRequests = await AppointmentRequest.countDocuments();
    console.log(`Appointment Requests: ${appointmentRequests}`);
    
    const linkedRequests = await AppointmentRequest.countDocuments({
      attachmentProof: { $ne: null }
    });
    console.log(`Appointment Requests with Attachments: ${linkedRequests}`);

    // Show sample data
    console.log("\n📋 Sample Appointment Requests:");
    const sampleRequests = await AppointmentRequest.find()
      .populate("attachmentProof")
      .limit(3);
    
    sampleRequests.forEach((req, index) => {
      console.log(`${index + 1}. ${req.transactionNumber} - ${req.name}`);
      console.log(`   Email: ${req.emailAddress}`);
      console.log(`   Request Type: ${req.requestType}`);
      console.log(`   Has Attachment: ${req.attachmentProof ? 'Yes' : 'No'}`);
      if (req.attachmentProof) {
        console.log(`   Attachment Files: ${req.attachmentProof.files?.length || 0}`);
      }
      console.log("");
    });

  } catch (error) {
    console.error("❌ Debug error:", error);
  }
};

// Main execution
const main = async () => {
  await connectDB();
  
  const command = process.argv[2];
  
  switch (command) {
    case "migrate":
      await migrateAttachments();
      break;
    case "debug":
      await debugCurrentState();
      break;
    default:
      console.log("Usage:");
      console.log("  node migrateAttachments.js migrate  - Link existing attachments to appointment requests");
      console.log("  node migrateAttachments.js debug    - Show current database state");
      break;
  }
  
  await mongoose.disconnect();
  console.log("✅ Disconnected from MongoDB");
};

main().catch(console.error);
