const AppointmentRequest = require("../../../models/adminSideSchema/dashboard/appointmentRequestSchema");
const Attachment = require("../../../models/appointmentSchema/attachmentSchema");

// Get all appointment requests with proper attachment data
const getAppointmentRequestsWithAttachments = async (req, res) => {
  try {
    console.log("🔍 DEBUG: Fetching appointment requests with attachments...");

    // Get all appointment requests and populate attachment references
    const appointmentRequests = await AppointmentRequest.find()
      .populate({
        path: "attachmentProof",
        model: "Attachment",
        select: "files",
        populate: {
          path: "files.student",
          model: "Student",
          select: "transactionNumber firstName surname",
        },
      })
      .sort({ createdAt: -1 });

    console.log(
      `🔍 DEBUG: Found ${appointmentRequests.length} appointment requests`
    );

    // Process each appointment request to extract attachment URLs
    const processedRequests = appointmentRequests.map((request) => {
      console.log(
        `🔍 DEBUG: Processing request ${request.transactionNumber}:`,
        {
          hasAttachmentProof: !!request.attachmentProof,
          attachmentProofId: request.attachmentProof?._id,
          filesCount: request.attachmentProof?.files?.length || 0,
        }
      );

      let attachmentUrls = [];

      if (request.attachmentProof && request.attachmentProof.files) {
        attachmentUrls = request.attachmentProof.files
          .filter((file) => file && file.path)
          .map((file) => {
            console.log("🔍 DEBUG: Processing attachment file:", {
              filename: file.filename,
              path: file.path,
              isCloudinaryUrl: file.path.startsWith(
                "https://res.cloudinary.com"
              ),
            });

            // Return the full Cloudinary URL directly from database
            if (file.path.startsWith("https://res.cloudinary.com")) {
              return {
                url: file.path,
                filename: file.filename,
                mimetype: file.mimetype,
                size: file.size,
              };
            } else {
              // Fallback: construct URL if needed (for legacy data)
              const publicId = file.path.replace(/\.[^/.]+$/, "");
              const timestampMatch = publicId.match(/-(\d+)$/);

              if (timestampMatch) {
                const fullTimestamp = timestampMatch[1];
                const versionTimestamp = fullTimestamp.substring(0, 10);
                const fullUrl = `https://res.cloudinary.com/dp9hjzio8/image/upload/v${versionTimestamp}/appointment-system/attachments/${publicId}`;

                return {
                  url: fullUrl,
                  filename: file.filename,
                  mimetype: file.mimetype,
                  size: file.size,
                };
              } else {
                console.warn("⚠️ Could not process attachment path:", file.path);
                return {
                  url: null,
                  filename: file.filename,
                  mimetype: file.mimetype,
                  size: file.size,
                };
              }
            }
          });
      }

      console.log(
        `🔍 DEBUG: Final attachment URLs for ${request.transactionNumber}:`,
        attachmentUrls
      );

      return {
        _id: request._id,
        transactionNumber: request.transactionNumber,
        name: request.name,
        lastSYAttended: request.lastSYAttended,
        programGradeStrand: request.programGradeStrand,
        contactNo: request.contactNo,
        emailAddress: request.emailAddress,
        requestType: request.requestType,
        dateOfRequest: request.dateOfRequest,
        claimingMethod: request.claimingMethod,
        createdAt: request.createdAt,
        attachments: attachmentUrls,
        // Legacy field for backward compatibility
        attachmentProofLegacy: request.attachmentProofLegacy,
      };
    });

    console.log(
      `✅ Successfully processed ${processedRequests.length} appointment requests`
    );

    res.status(200).json({
      success: true,
      data: processedRequests,
      count: processedRequests.length,
    });
  } catch (error) {
    console.error("❌ Error fetching appointment requests:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching appointment requests",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get single appointment request by transaction number
const getAppointmentRequestByTransaction = async (req, res) => {
  try {
    const { transactionNumber } = req.params;

    const appointmentRequest = await AppointmentRequest.findOne({
      transactionNumber,
    }).populate({
      path: "attachmentProof",
      model: "Attachment",
      select: "files",
    });

    if (!appointmentRequest) {
      return res.status(404).json({
        success: false,
        message: "Appointment request not found",
      });
    }

    // Process attachments
    let attachmentUrls = [];
    if (appointmentRequest.attachmentProof && appointmentRequest.attachmentProof.files) {
      attachmentUrls = appointmentRequest.attachmentProof.files
        .filter((file) => file && file.path)
        .map((file) => ({
          url: file.path.startsWith("https://res.cloudinary.com")
            ? file.path
            : `https://res.cloudinary.com/dp9hjzio8/image/upload/v${file.path.match(/-(\d+)$/)?.[1]?.substring(0, 10) || Date.now().toString().substring(0, 10)}/appointment-system/attachments/${file.path.replace(/\.[^/.]+$/, "")}`,
          filename: file.filename,
          mimetype: file.mimetype,
          size: file.size,
        }));
    }

    const processedRequest = {
      ...appointmentRequest.toObject(),
      attachments: attachmentUrls,
    };

    res.status(200).json({
      success: true,
      data: processedRequest,
    });
  } catch (error) {
    console.error("❌ Error fetching appointment request:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching appointment request",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  getAppointmentRequestsWithAttachments,
  getAppointmentRequestByTransaction,
};
