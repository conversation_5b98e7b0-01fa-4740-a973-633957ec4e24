0000000000000000000000000000000000000000 7aeee4ab51229e9dbc3d10b05b00f8f9f5aeae94 <PERSON> <<EMAIL>> 1745925922 +0800	commit (initial): first commit
7aeee4ab51229e9dbc3d10b05b00f8f9f5aeae94 0000000000000000000000000000000000000000 <PERSON> <<EMAIL>> 1745925922 +0800	Branch: renamed refs/heads/master to refs/heads/main
0000000000000000000000000000000000000000 7aeee4ab51229e9dbc3d10b05b00f8f9f5aeae94 <PERSON> <<EMAIL>> 1745925922 +0800	Branch: renamed refs/heads/master to refs/heads/main
7aeee4ab51229e9dbc3d10b05b00f8f9f5aeae94 7f2c5a99474f9d5b4d829d03bb4be7e9ecb9db8d <PERSON> <<EMAIL>> 1745925978 +0800	commit: backend
7f2c5a99474f9d5b4d829d03bb4be7e9ecb9db8d 52639ff8505db869439fd0538ebb4bdfce89bed1 Rafael Ramos <<EMAIL>> 1746455879 +0800	commit: backend for the frontend
52639ff8505db869439fd0538ebb4bdfce89bed1 180cb79e52584e2ed5174d779c018407c73cf6fc Rafael Ramos <<EMAIL>> 1746456028 +0800	commit: hide .env
180cb79e52584e2ed5174d779c018407c73cf6fc dfd263a1b2f5a59364fddc6a5a8eca3135886695 Rafael Ramos <<EMAIL>> 1746456097 +0800	commit: Remove .env from tracking
dfd263a1b2f5a59364fddc6a5a8eca3135886695 5e7efed206c726b81185a7d9424c928666dbf741 Rafael Ramos <<EMAIL>> 1746781408 +0800	commit: Signin & Signup
5e7efed206c726b81185a7d9424c928666dbf741 5e7efed206c726b81185a7d9424c928666dbf741 Rafael Ramos <<EMAIL>> 1746782249 +0800	checkout: moving from main to docreq
5e7efed206c726b81185a7d9424c928666dbf741 3884e943ee8da5086c213540292bfc04884eb76d Rafael Ramos <<EMAIL>> 1747064414 +0800	commit: DocsReques
3884e943ee8da5086c213540292bfc04884eb76d 5e7efed206c726b81185a7d9424c928666dbf741 Rafael Ramos <<EMAIL>> 1747064422 +0800	checkout: moving from docreq to main
5e7efed206c726b81185a7d9424c928666dbf741 3884e943ee8da5086c213540292bfc04884eb76d Rafael Ramos <<EMAIL>> 1747064431 +0800	merge docreq: Fast-forward
3884e943ee8da5086c213540292bfc04884eb76d 81f993c3dc5043b16898f5df393eb05944a81b05 Rafael Ramos <<EMAIL>> 1747547852 +0800	commit: backend
81f993c3dc5043b16898f5df393eb05944a81b05 81f993c3dc5043b16898f5df393eb05944a81b05 Rafael Ramos <<EMAIL>> 1747656949 +0800	checkout: moving from main to signinup
81f993c3dc5043b16898f5df393eb05944a81b05 86b989c7f68534c1fb104fdb1b47311d734da37f Rafael Ramos <<EMAIL>> 1747660157 +0800	commit: signup and signin
86b989c7f68534c1fb104fdb1b47311d734da37f 81f993c3dc5043b16898f5df393eb05944a81b05 Rafael Ramos <<EMAIL>> 1747660165 +0800	checkout: moving from signinup to main
81f993c3dc5043b16898f5df393eb05944a81b05 86b989c7f68534c1fb104fdb1b47311d734da37f Rafael Ramos <<EMAIL>> 1747660173 +0800	merge signinup: Fast-forward
86b989c7f68534c1fb104fdb1b47311d734da37f 86b989c7f68534c1fb104fdb1b47311d734da37f Rafael Ramos <<EMAIL>> 1747667555 +0800	checkout: moving from main to test
86b989c7f68534c1fb104fdb1b47311d734da37f 0b991eea5fa9f4576e3ebee1425f750daf8ede85 Rafael Ramos <<EMAIL>> 1747673357 +0800	commit: wfajkh
0b991eea5fa9f4576e3ebee1425f750daf8ede85 86b989c7f68534c1fb104fdb1b47311d734da37f Rafael Ramos <<EMAIL>> 1747673361 +0800	checkout: moving from test to main
86b989c7f68534c1fb104fdb1b47311d734da37f b7c2269764b79300f6da53e9b9d3a26e114ea4ba Rafael Ramos <<EMAIL>> 1747673677 +0800	commit: delete schema
b7c2269764b79300f6da53e9b9d3a26e114ea4ba b7c2269764b79300f6da53e9b9d3a26e114ea4ba Rafael Ramos <<EMAIL>> 1747725716 +0800	checkout: moving from main to sign
b7c2269764b79300f6da53e9b9d3a26e114ea4ba b7c2269764b79300f6da53e9b9d3a26e114ea4ba Rafael Ramos <<EMAIL>> 1747728848 +0800	checkout: moving from sign to main
b7c2269764b79300f6da53e9b9d3a26e114ea4ba b7c2269764b79300f6da53e9b9d3a26e114ea4ba Rafael Ramos <<EMAIL>> 1747728946 +0800	checkout: moving from main to test
b7c2269764b79300f6da53e9b9d3a26e114ea4ba b7c2269764b79300f6da53e9b9d3a26e114ea4ba Rafael Ramos <<EMAIL>> 1747744047 +0800	checkout: moving from test to main
b7c2269764b79300f6da53e9b9d3a26e114ea4ba 8a65f42a69100cd1e221dc25777d74d14c65cd5d Rafael Ramos <<EMAIL>> 1747744307 +0800	commit: minor changes
8a65f42a69100cd1e221dc25777d74d14c65cd5d 8a65f42a69100cd1e221dc25777d74d14c65cd5d Rafael Ramos <<EMAIL>> 1747744340 +0800	checkout: moving from main to googlesignin
8a65f42a69100cd1e221dc25777d74d14c65cd5d 8a65f42a69100cd1e221dc25777d74d14c65cd5d Rafael Ramos <<EMAIL>> 1747744426 +0800	checkout: moving from googlesignin to main
8a65f42a69100cd1e221dc25777d74d14c65cd5d 8a65f42a69100cd1e221dc25777d74d14c65cd5d Rafael Ramos <<EMAIL>> 1747744474 +0800	checkout: moving from main to googlesignin
8a65f42a69100cd1e221dc25777d74d14c65cd5d 94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 Rafael Ramos <<EMAIL>> 1747746044 +0800	commit: Google Sign In
94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 8a65f42a69100cd1e221dc25777d74d14c65cd5d Rafael Ramos <<EMAIL>> 1747747275 +0800	checkout: moving from googlesignin to main
8a65f42a69100cd1e221dc25777d74d14c65cd5d 94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 Rafael Ramos <<EMAIL>> 1747747283 +0800	merge googlesignin: Fast-forward
94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 0011318691bab00e8f76d592893b23b2d5b61c03 Rafael Ramos <<EMAIL>> 1747748847 +0800	commit: updated signin
0011318691bab00e8f76d592893b23b2d5b61c03 0011318691bab00e8f76d592893b23b2d5b61c03 Rafael Ramos <<EMAIL>> 1747749193 +0800	checkout: moving from main to connect
0011318691bab00e8f76d592893b23b2d5b61c03 0011318691bab00e8f76d592893b23b2d5b61c03 Rafael Ramos <<EMAIL>> 1747760493 +0800	checkout: moving from connect to main
0011318691bab00e8f76d592893b23b2d5b61c03 0ad46f799c38af287bb9f6d41615e60d5ba20fab Rafael Ramos <<EMAIL>> 1747760516 +0800	commit: doc request
0ad46f799c38af287bb9f6d41615e60d5ba20fab 0ad46f799c38af287bb9f6d41615e60d5ba20fab Rafael Ramos <<EMAIL>> 1747760991 +0800	checkout: moving from main to attach
0ad46f799c38af287bb9f6d41615e60d5ba20fab d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747769011 +0800	commit: adding schedule
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd 0ad46f799c38af287bb9f6d41615e60d5ba20fab Rafael Ramos <<EMAIL>> 1747769018 +0800	checkout: moving from attach to main
0ad46f799c38af287bb9f6d41615e60d5ba20fab d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747769033 +0800	merge attach: Fast-forward
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747769256 +0800	checkout: moving from main to bookSchedule
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747771457 +0800	checkout: moving from bookSchedule to main
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747771864 +0800	reset: moving to HEAD
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747772106 +0800	checkout: moving from main to test
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd c4355e256cf96c2349c6f93ffdcc223f95889c5d Rafael Ramos <<EMAIL>> 1747804764 +0800	commit: update
c4355e256cf96c2349c6f93ffdcc223f95889c5d c4355e256cf96c2349c6f93ffdcc223f95889c5d Rafael Ramos <<EMAIL>> 1747804788 +0800	reset: moving to HEAD
c4355e256cf96c2349c6f93ffdcc223f95889c5d d6243add3d3b35357a09bc2912ec29e3fb8d8117 Rafael Ramos <<EMAIL>> 1747804867 +0800	commit: hide node modules
d6243add3d3b35357a09bc2912ec29e3fb8d8117 d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd Rafael Ramos <<EMAIL>> 1747804951 +0800	checkout: moving from test to main
d0f8b60b5db2ffb266bebef6d13c25e00eba3bbd d6243add3d3b35357a09bc2912ec29e3fb8d8117 Rafael Ramos <<EMAIL>> 1747804966 +0800	merge test: Fast-forward
d6243add3d3b35357a09bc2912ec29e3fb8d8117 d6243add3d3b35357a09bc2912ec29e3fb8d8117 Rafael Ramos <<EMAIL>> 1747805016 +0800	reset: moving to HEAD
d6243add3d3b35357a09bc2912ec29e3fb8d8117 8dff11a0e6e52a4012bb4761148cf7288c466e74 Rafael Ramos <<EMAIL>> 1747805617 +0800	commit: adding packages
8dff11a0e6e52a4012bb4761148cf7288c466e74 94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 Rafael Ramos <<EMAIL>> 1747805703 +0800	checkout: moving from main to googlesignin
94aa91bfb311ca8e7172db2ea43718a6eaa9ea64 8dff11a0e6e52a4012bb4761148cf7288c466e74 Rafael Ramos <<EMAIL>> 1747805992 +0800	checkout: moving from googlesignin to main
8dff11a0e6e52a4012bb4761148cf7288c466e74 8dff11a0e6e52a4012bb4761148cf7288c466e74 Rafael Ramos <<EMAIL>> 1747806029 +0800	reset: moving to HEAD
8dff11a0e6e52a4012bb4761148cf7288c466e74 4f06731f95c0a8a196bbc8b0f955302fca06161f Rafael Ramos <<EMAIL>> 1747807595 +0800	commit: error handling
4f06731f95c0a8a196bbc8b0f955302fca06161f a96afd3fa1ddae9e25e721eb6eae1c19657899de Rafael Ramos <<EMAIL>> 1747808217 +0800	commit: update
a96afd3fa1ddae9e25e721eb6eae1c19657899de a96afd3fa1ddae9e25e721eb6eae1c19657899de Rafael Ramos <<EMAIL>> 1747834378 +0800	checkout: moving from main to upload
a96afd3fa1ddae9e25e721eb6eae1c19657899de 9b8fcf8546676073c6429b9379b8b87d30649903 Rafael Ramos <<EMAIL>> 1747836947 +0800	commit: handling file upload
9b8fcf8546676073c6429b9379b8b87d30649903 a96afd3fa1ddae9e25e721eb6eae1c19657899de Rafael Ramos <<EMAIL>> 1747836961 +0800	checkout: moving from upload to main
a96afd3fa1ddae9e25e721eb6eae1c19657899de 9b8fcf8546676073c6429b9379b8b87d30649903 Rafael Ramos <<EMAIL>> 1747836971 +0800	merge upload: Fast-forward
9b8fcf8546676073c6429b9379b8b87d30649903 9b8fcf8546676073c6429b9379b8b87d30649903 Rafael Ramos <<EMAIL>> 1747837202 +0800	checkout: moving from main to booking
9b8fcf8546676073c6429b9379b8b87d30649903 f487b9864388e1fddf5850f4da3f12104bbec0ee Rafael Ramos <<EMAIL>> 1747856832 +0800	commit: updated
f487b9864388e1fddf5850f4da3f12104bbec0ee 9b8fcf8546676073c6429b9379b8b87d30649903 Rafael Ramos <<EMAIL>> 1747856887 +0800	checkout: moving from booking to main
9b8fcf8546676073c6429b9379b8b87d30649903 72786bed34ed6ab06664719b2e9b00bb0a5fedeb Rafael Ramos <<EMAIL>> 1747856895 +0800	pull origin main: Fast-forward
72786bed34ed6ab06664719b2e9b00bb0a5fedeb aede4077f3082df3b7378b066469919de2a6f6ea Rafael Ramos <<EMAIL>> 1747856972 +0800	commit (merge): update
aede4077f3082df3b7378b066469919de2a6f6ea 27502a13c3609e593eaeb582295ed0206eb4717e Rafael Ramos <<EMAIL>> 1747870606 +0800	commit: mongodb uri
27502a13c3609e593eaeb582295ed0206eb4717e 37e2c76f1f0e8f025244213293c0628d8b1d0039 Rafael Ramos <<EMAIL>> 1747935416 +0800	commit: changing the schema in booking
37e2c76f1f0e8f025244213293c0628d8b1d0039 37e2c76f1f0e8f025244213293c0628d8b1d0039 Rafael Ramos <<EMAIL>> 1747935598 +0800	checkout: moving from main to adminside
37e2c76f1f0e8f025244213293c0628d8b1d0039 f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 Rafael Ramos <<EMAIL>> 1747938293 +0800	commit: dynamic schedule
f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 37e2c76f1f0e8f025244213293c0628d8b1d0039 Rafael Ramos <<EMAIL>> 1747938301 +0800	checkout: moving from adminside to main
37e2c76f1f0e8f025244213293c0628d8b1d0039 f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 Rafael Ramos <<EMAIL>> 1747938316 +0800	merge adminside: Fast-forward
f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 Rafael Ramos <<EMAIL>> 1747940546 +0800	checkout: moving from main to admin
f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 d8216af3429e929088b71d8a6d80f7fbb63ffdd9 Rafael Ramos <<EMAIL>> 1748056141 +0800	commit: minor changes
d8216af3429e929088b71d8a6d80f7fbb63ffdd9 f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 Rafael Ramos <<EMAIL>> 1748056144 +0800	checkout: moving from admin to main
f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 859b5b0db699bd0a320ab68d8e84f4cf4c4234cc Rafael Ramos <<EMAIL>> 1748056615 +0800	pull origin main: Fast-forward
859b5b0db699bd0a320ab68d8e84f4cf4c4234cc f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 Rafael Ramos <<EMAIL>> 1748057082 +0800	checkout: moving from main to adminside
f9ccc8d306b2afa8419d84fa2cbc026cd90bed35 859b5b0db699bd0a320ab68d8e84f4cf4c4234cc Rafael Ramos <<EMAIL>> 1748058030 +0800	checkout: moving from adminside to main
859b5b0db699bd0a320ab68d8e84f4cf4c4234cc 859b5b0db699bd0a320ab68d8e84f4cf4c4234cc Rafael Ramos <<EMAIL>> 1748170140 +0800	checkout: moving from main to password
859b5b0db699bd0a320ab68d8e84f4cf4c4234cc 62cf3f7167735103fbcbee9fd58aeaa4b6bbce7d Rafael Ramos <<EMAIL>> 1748233504 +0800	commit: otp
62cf3f7167735103fbcbee9fd58aeaa4b6bbce7d e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 Rafael Ramos <<EMAIL>> 1748238797 +0800	commit: minor changes for otp
e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 859b5b0db699bd0a320ab68d8e84f4cf4c4234cc Rafael Ramos <<EMAIL>> 1748267565 +0800	checkout: moving from password to main
859b5b0db699bd0a320ab68d8e84f4cf4c4234cc e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 Rafael Ramos <<EMAIL>> 1748267569 +0800	merge password: Fast-forward
e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 Rafael Ramos <<EMAIL>> 1748267618 +0800	checkout: moving from main to revision
e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748277269 +0800	commit: changing components
9acc6b44452d1bc337ed1df3e445de406996c438 e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 Rafael Ramos <<EMAIL>> 1748277380 +0800	checkout: moving from revision to main
e4ade73eb3ca7f5884f11ec06e3b6daa6aae8a31 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748277389 +0800	merge revision: Fast-forward
9acc6b44452d1bc337ed1df3e445de406996c438 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748277483 +0800	checkout: moving from main to userprofile
9acc6b44452d1bc337ed1df3e445de406996c438 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748282971 +0800	checkout: moving from userprofile to main
9acc6b44452d1bc337ed1df3e445de406996c438 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748283098 +0800	checkout: moving from main to updateprofile
9acc6b44452d1bc337ed1df3e445de406996c438 8a81bc7ea9e73d0e496f949e1ebd7d26d1ba175f Rafael Ramos <<EMAIL>> 1748288520 +0800	commit: userprofile handling
8a81bc7ea9e73d0e496f949e1ebd7d26d1ba175f b269f8b309712b5274158f48c4fbc01ed9595a78 Rafael Ramos <<EMAIL>> 1748354211 +0800	commit: profile folder
b269f8b309712b5274158f48c4fbc01ed9595a78 9acc6b44452d1bc337ed1df3e445de406996c438 Rafael Ramos <<EMAIL>> 1748354220 +0800	checkout: moving from updateprofile to main
9acc6b44452d1bc337ed1df3e445de406996c438 b269f8b309712b5274158f48c4fbc01ed9595a78 Rafael Ramos <<EMAIL>> 1748354228 +0800	merge updateprofile: Fast-forward
b269f8b309712b5274158f48c4fbc01ed9595a78 b269f8b309712b5274158f48c4fbc01ed9595a78 Rafael Ramos <<EMAIL>> 1748358170 +0800	checkout: moving from main to notif
b269f8b309712b5274158f48c4fbc01ed9595a78 b269f8b309712b5274158f48c4fbc01ed9595a78 Rafael Ramos <<EMAIL>> 1748360837 +0800	reset: moving to HEAD
b269f8b309712b5274158f48c4fbc01ed9595a78 b269f8b309712b5274158f48c4fbc01ed9595a78 Rafael Ramos <<EMAIL>> 1748360850 +0800	checkout: moving from notif to main
b269f8b309712b5274158f48c4fbc01ed9595a78 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748360870 +0800	pull origin main: Fast-forward
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748360881 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748361565 +0800	checkout: moving from main to appointmentstatus
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748363883 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748363899 +0800	checkout: moving from appointmentstatus to main
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748363922 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748364277 +0800	checkout: moving from main to status
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748365631 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748365647 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748366737 +0800	reset: moving to HEAD
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748368163 +0800	commit: stable Transaction number
27297f92aae3a32c440366c56e03982d8ad91f9a d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 Rafael Ramos <<EMAIL>> 1748368374 +0800	checkout: moving from status to main
d8fd8b4c0f5a6cce9c8c8452f9daf66a24c774d8 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748368383 +0800	merge status: Fast-forward
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748368414 +0800	checkout: moving from main to adminstatus
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748444849 +0800	checkout: moving from adminstatus to main
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748444858 +0800	checkout: moving from main to archive
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748448961 +0800	reset: moving to HEAD
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748448988 +0800	checkout: moving from archive to main
27297f92aae3a32c440366c56e03982d8ad91f9a 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748449086 +0800	checkout: moving from main to statusupdate
27297f92aae3a32c440366c56e03982d8ad91f9a 028a464e97d77e8730392690dfc9598d9b7b538c Rafael Ramos <<EMAIL>> 1748450181 +0800	commit: dynamic status button
028a464e97d77e8730392690dfc9598d9b7b538c 27297f92aae3a32c440366c56e03982d8ad91f9a Rafael Ramos <<EMAIL>> 1748452347 +0800	checkout: moving from statusupdate to main
27297f92aae3a32c440366c56e03982d8ad91f9a 028a464e97d77e8730392690dfc9598d9b7b538c Rafael Ramos <<EMAIL>> 1748452385 +0800	merge statusupdate: Fast-forward
028a464e97d77e8730392690dfc9598d9b7b538c 4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 Rafael Ramos <<EMAIL>> 1748490309 +0800	pull origin main: Fast-forward
4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 Rafael Ramos <<EMAIL>> 1748490648 +0800	checkout: moving from main to archive
4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 Rafael Ramos <<EMAIL>> 1748507169 +0800	reset: moving to HEAD
4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 Rafael Ramos <<EMAIL>> 1748507172 +0800	checkout: moving from archive to main
4e4b50dfdeea0506e6b906b8cc38b8d9dc5e39b2 a57a45a12ca070bf4fc1df5a890b7864ef7f971b Rafael Ramos <<EMAIL>> 1748507182 +0800	pull origin main: Fast-forward
a57a45a12ca070bf4fc1df5a890b7864ef7f971b a57a45a12ca070bf4fc1df5a890b7864ef7f971b Rafael Ramos <<EMAIL>> 1748523873 +0800	checkout: moving from main to graph
a57a45a12ca070bf4fc1df5a890b7864ef7f971b a57a45a12ca070bf4fc1df5a890b7864ef7f971b Rafael Ramos <<EMAIL>> 1748524721 +0800	checkout: moving from graph to main
a57a45a12ca070bf4fc1df5a890b7864ef7f971b a57a45a12ca070bf4fc1df5a890b7864ef7f971b Rafael Ramos <<EMAIL>> 1748524732 +0800	checkout: moving from main to graph
a57a45a12ca070bf4fc1df5a890b7864ef7f971b fb2c1f8fbaa86a20fc6dcf241d98e0efd1fc7e2c Rafael Ramos <<EMAIL>> 1748527534 +0800	commit: stats
fb2c1f8fbaa86a20fc6dcf241d98e0efd1fc7e2c 3f4dfffbc8abc06a8359a924318b96f76f4678c9 Rafael Ramos <<EMAIL>> 1748528122 +0800	commit: dynamic status
3f4dfffbc8abc06a8359a924318b96f76f4678c9 a57a45a12ca070bf4fc1df5a890b7864ef7f971b Rafael Ramos <<EMAIL>> 1748528145 +0800	checkout: moving from graph to main
a57a45a12ca070bf4fc1df5a890b7864ef7f971b 3f4dfffbc8abc06a8359a924318b96f76f4678c9 Rafael Ramos <<EMAIL>> 1748528150 +0800	merge graph: Fast-forward
3f4dfffbc8abc06a8359a924318b96f76f4678c9 3f4dfffbc8abc06a8359a924318b96f76f4678c9 Rafael Ramos <<EMAIL>> 1748528872 +0800	checkout: moving from main to notif
3f4dfffbc8abc06a8359a924318b96f76f4678c9 161b26914e4e926813de830883e5f198e41075a8 Rafael Ramos <<EMAIL>> 1748529802 +0800	commit: minor changes
161b26914e4e926813de830883e5f198e41075a8 46601db8c53d722e77bae4c64df94d682de98e4c Rafael Ramos <<EMAIL>> 1748533875 +0800	commit: sending email
46601db8c53d722e77bae4c64df94d682de98e4c 3f4dfffbc8abc06a8359a924318b96f76f4678c9 Rafael Ramos <<EMAIL>> 1748533912 +0800	checkout: moving from notif to main
3f4dfffbc8abc06a8359a924318b96f76f4678c9 46601db8c53d722e77bae4c64df94d682de98e4c Rafael Ramos <<EMAIL>> 1748533917 +0800	merge notif: Fast-forward
46601db8c53d722e77bae4c64df94d682de98e4c 9b5ac26918661d50af67941bd9f52019cf88ee57 Rafael Ramos <<EMAIL>> 1748564590 +0800	pull origin main: Fast-forward
9b5ac26918661d50af67941bd9f52019cf88ee57 9b5ac26918661d50af67941bd9f52019cf88ee57 Rafael Ramos <<EMAIL>> 1748608556 +0800	checkout: moving from main to render
9b5ac26918661d50af67941bd9f52019cf88ee57 9b5ac26918661d50af67941bd9f52019cf88ee57 Rafael Ramos <<EMAIL>> 1748630840 +0800	checkout: moving from render to main
9b5ac26918661d50af67941bd9f52019cf88ee57 5b6d469d1c6668182cb4fb7123a274e18b157c45 Rafael Ramos <<EMAIL>> 1748630847 +0800	pull origin main: Fast-forward
5b6d469d1c6668182cb4fb7123a274e18b157c45 09accbca203e6c2c4c151a025d2d0a93826628ae Rafael Ramos <<EMAIL>> 1748634114 +0800	commit: render backend
09accbca203e6c2c4c151a025d2d0a93826628ae a0aed8aacb8ad6118d84cd92c4ac190f267d8285 Rafael Ramos <<EMAIL>> 1748635275 +0800	commit: testing render
a0aed8aacb8ad6118d84cd92c4ac190f267d8285 f9648b70a96228d03cbc09e35933d07d0dda4fd4 Rafael Ramos <<EMAIL>> 1748635981 +0800	commit: testing render
f9648b70a96228d03cbc09e35933d07d0dda4fd4 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748636283 +0800	commit: testing render
4ba1a557579c8e098c742b0577187b456a0373d7 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748649537 +0800	reset: moving to HEAD
4ba1a557579c8e098c742b0577187b456a0373d7 3f4dfffbc8abc06a8359a924318b96f76f4678c9 Rafael Ramos <<EMAIL>> 1748651950 +0800	checkout: moving from main to graph
3f4dfffbc8abc06a8359a924318b96f76f4678c9 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748652351 +0800	checkout: moving from graph to main
4ba1a557579c8e098c742b0577187b456a0373d7 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748654089 +0800	checkout: moving from main to slot
4ba1a557579c8e098c742b0577187b456a0373d7 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748654533 +0800	checkout: moving from slot to main
4ba1a557579c8e098c742b0577187b456a0373d7 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748663202 +0800	checkout: moving from main to render
4ba1a557579c8e098c742b0577187b456a0373d7 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748797401 +0800	checkout: moving from render to main
4ba1a557579c8e098c742b0577187b456a0373d7 6e49897036cddc20b8d28e86d76a6ecc44aa6c05 Rafael Ramos <<EMAIL>> 1748797410 +0800	pull origin main: Fast-forward
6e49897036cddc20b8d28e86d76a6ecc44aa6c05 4ba1a557579c8e098c742b0577187b456a0373d7 Rafael Ramos <<EMAIL>> 1748798562 +0800	checkout: moving from main to render
4ba1a557579c8e098c742b0577187b456a0373d7 6e49897036cddc20b8d28e86d76a6ecc44aa6c05 Rafael Ramos <<EMAIL>> 1748800784 +0800	checkout: moving from render to main
6e49897036cddc20b8d28e86d76a6ecc44aa6c05 6e49897036cddc20b8d28e86d76a6ecc44aa6c05 Rafael Ramos <<EMAIL>> 1748800788 +0800	checkout: moving from main to main
6e49897036cddc20b8d28e86d76a6ecc44aa6c05 fafc2ef74491086698ba5e94138c9562cc9f0626 Rafael Ramos <<EMAIL>> 1748800811 +0800	pull origin main: Fast-forward
fafc2ef74491086698ba5e94138c9562cc9f0626 a4e542f1e208880e6917d3e364d33ff02a5dff42 Rafael Ramos <<EMAIL>> 1748807630 +0800	commit: server.js
a4e542f1e208880e6917d3e364d33ff02a5dff42 a4e542f1e208880e6917d3e364d33ff02a5dff42 Rafael Ramos <<EMAIL>> 1748807642 +0800	checkout: moving from main to bug
a4e542f1e208880e6917d3e364d33ff02a5dff42 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1748813970 +0800	commit: minor changes
0c99dc19a865f3d0831a5b0b231d42a86a21a649 a4e542f1e208880e6917d3e364d33ff02a5dff42 Rafael Ramos <<EMAIL>> 1748813973 +0800	checkout: moving from bug to main
a4e542f1e208880e6917d3e364d33ff02a5dff42 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1748813977 +0800	merge bug: Fast-forward
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1748841412 +0800	checkout: moving from main to bugs
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0a4189463c075a75f41d901068413f127d326e71 Rafael Ramos <<EMAIL>> 1748864799 +0800	commit: trial and error
0a4189463c075a75f41d901068413f127d326e71 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1748864805 +0800	checkout: moving from bugs to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1748961962 +0800	checkout: moving from main to error
0c99dc19a865f3d0831a5b0b231d42a86a21a649 d10bcfcfbac5cb59db5338f617e32d20790aa11b Rafael Ramos <<EMAIL>> 1748999646 +0800	commit: duplicate boking
d10bcfcfbac5cb59db5338f617e32d20790aa11b 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749001157 +0800	checkout: moving from error to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749001348 +0800	checkout: moving from main to profile
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749002767 +0800	checkout: moving from profile to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749002937 +0800	checkout: moving from main to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749003033 +0800	checkout: moving from bug to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749223888 +0800	checkout: moving from main to errors
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749229352 +0800	reset: moving to HEAD
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749229365 +0800	checkout: moving from errors to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749229428 +0800	checkout: moving from main to bugs
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749254763 +0800	reset: moving to HEAD
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749254790 +0800	checkout: moving from bugs to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749254884 +0800	checkout: moving from bug to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749255110 +0800	checkout: moving from main to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749354646 +0800	checkout: moving from bug to main
0c99dc19a865f3d0831a5b0b231d42a86a21a649 e81fffd917cadabe8d6c2c720f790fad73c43158 Rafael Ramos <<EMAIL>> 1749354737 +0800	pull origin main: Fast-forward
e81fffd917cadabe8d6c2c720f790fad73c43158 e81fffd917cadabe8d6c2c720f790fad73c43158 Rafael Ramos <<EMAIL>> 1749378728 +0800	checkout: moving from main to error
e81fffd917cadabe8d6c2c720f790fad73c43158 498707ccec87186762fff41c9503cd0228900cc6 Rafael Ramos <<EMAIL>> 1749386112 +0800	commit: ??
498707ccec87186762fff41c9503cd0228900cc6 e81fffd917cadabe8d6c2c720f790fad73c43158 Rafael Ramos <<EMAIL>> 1749386129 +0800	checkout: moving from error to main
e81fffd917cadabe8d6c2c720f790fad73c43158 e81fffd917cadabe8d6c2c720f790fad73c43158 Rafael Ramos <<EMAIL>> 1749560821 +0800	reset: moving to HEAD
e81fffd917cadabe8d6c2c720f790fad73c43158 c83d8fcddc03568a1e7a878fd1b974250acaff66 Rafael Ramos <<EMAIL>> 1749560837 +0800	pull origin main: Fast-forward
c83d8fcddc03568a1e7a878fd1b974250acaff66 c83d8fcddc03568a1e7a878fd1b974250acaff66 Rafael Ramos <<EMAIL>> 1749561047 +0800	checkout: moving from main to debugging
c83d8fcddc03568a1e7a878fd1b974250acaff66 cbf3989d875ca58afcee550e25fc6e896e930e03 Rafael Ramos <<EMAIL>> 1749563162 +0800	commit: cloudinary
cbf3989d875ca58afcee550e25fc6e896e930e03 c83d8fcddc03568a1e7a878fd1b974250acaff66 Rafael Ramos <<EMAIL>> 1749563169 +0800	checkout: moving from debugging to main
c83d8fcddc03568a1e7a878fd1b974250acaff66 cbf3989d875ca58afcee550e25fc6e896e930e03 Rafael Ramos <<EMAIL>> 1749563174 +0800	merge debugging: Fast-forward
cbf3989d875ca58afcee550e25fc6e896e930e03 e06f6f73d6d0e650e0a77e59ee2851d8952fe6aa Rafael Ramos <<EMAIL>> 1749564214 +0800	commit: Fix Cloudinary configuration with fallback and better error handling
e06f6f73d6d0e650e0a77e59ee2851d8952fe6aa e7cecefd92c604a0bbdf7d3be529a04925b3f9fc Rafael Ramos <<EMAIL>> 1749564698 +0800	commit: Fix userProfileRoute to use Cloudinary storage and add debugging
e7cecefd92c604a0bbdf7d3be529a04925b3f9fc e7cecefd92c604a0bbdf7d3be529a04925b3f9fc Rafael Ramos <<EMAIL>> 1749565012 +0800	checkout: moving from main to buug
e7cecefd92c604a0bbdf7d3be529a04925b3f9fc 9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 Rafael Ramos <<EMAIL>> 1749567631 +0800	commit: remember me
9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 e7cecefd92c604a0bbdf7d3be529a04925b3f9fc Rafael Ramos <<EMAIL>> 1749567687 +0800	checkout: moving from buug to main
e7cecefd92c604a0bbdf7d3be529a04925b3f9fc 9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 Rafael Ramos <<EMAIL>> 1749567694 +0800	merge buug: Fast-forward
9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 59b59a54b80030c3248393bb870df8e4f391097f Rafael Ramos <<EMAIL>> 1749568164 +0800	commit: deleting the unnecessary code
59b59a54b80030c3248393bb870df8e4f391097f 9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 Rafael Ramos <<EMAIL>> 1749568198 +0800	checkout: moving from main to buug
9ed8db81f8ab4a35c491fdeb4a696171a5b5dd78 59b59a54b80030c3248393bb870df8e4f391097f Rafael Ramos <<EMAIL>> 1749568288 +0800	checkout: moving from buug to main
59b59a54b80030c3248393bb870df8e4f391097f 59b59a54b80030c3248393bb870df8e4f391097f Rafael Ramos <<EMAIL>> 1749568297 +0800	checkout: moving from main to dashboard
59b59a54b80030c3248393bb870df8e4f391097f d931d7ffb6290fac928f21408169cbcb9a080609 Rafael Ramos <<EMAIL>> 1749570324 +0800	commit: Overview of the month
d931d7ffb6290fac928f21408169cbcb9a080609 59b59a54b80030c3248393bb870df8e4f391097f Rafael Ramos <<EMAIL>> 1749570331 +0800	checkout: moving from dashboard to main
59b59a54b80030c3248393bb870df8e4f391097f d931d7ffb6290fac928f21408169cbcb9a080609 Rafael Ramos <<EMAIL>> 1749570334 +0800	merge dashboard: Fast-forward
d931d7ffb6290fac928f21408169cbcb9a080609 d931d7ffb6290fac928f21408169cbcb9a080609 Rafael Ramos <<EMAIL>> 1749573233 +0800	checkout: moving from main to dashboard
d931d7ffb6290fac928f21408169cbcb9a080609 fdadd8f67196448ea574ecb2b89d3424a72aad97 Rafael Ramos <<EMAIL>> 1749575246 +0800	commit: duplicate
fdadd8f67196448ea574ecb2b89d3424a72aad97 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749576423 +0800	commit: dynamic name in notif
50fdafd77a9dd7fecde10fb09d012f83a0229923 d931d7ffb6290fac928f21408169cbcb9a080609 Rafael Ramos <<EMAIL>> 1749576431 +0800	checkout: moving from dashboard to main
d931d7ffb6290fac928f21408169cbcb9a080609 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749576438 +0800	merge dashboard: Fast-forward
50fdafd77a9dd7fecde10fb09d012f83a0229923 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749576725 +0800	checkout: moving from main to dashboard
50fdafd77a9dd7fecde10fb09d012f83a0229923 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749577211 +0800	checkout: moving from dashboard to main
50fdafd77a9dd7fecde10fb09d012f83a0229923 0e8c20cde2196c420d3e7974d8412b9f8a6cbabd Rafael Ramos <<EMAIL>> 1749578433 +0800	commit: delete
0e8c20cde2196c420d3e7974d8412b9f8a6cbabd 0e8c20cde2196c420d3e7974d8412b9f8a6cbabd Rafael Ramos <<EMAIL>> 1749579341 +0800	reset: moving to HEAD
0e8c20cde2196c420d3e7974d8412b9f8a6cbabd e1592efb6ec751310f4594fc3720d39f4835eeca Rafael Ramos <<EMAIL>> 1749579641 +0800	commit: disable duplicate
e1592efb6ec751310f4594fc3720d39f4835eeca 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749580233 +0800	checkout: moving from main to dashboard
50fdafd77a9dd7fecde10fb09d012f83a0229923 e1592efb6ec751310f4594fc3720d39f4835eeca Rafael Ramos <<EMAIL>> 1749580512 +0800	checkout: moving from dashboard to main
e1592efb6ec751310f4594fc3720d39f4835eeca 98bd904c85997d9f76abfaaea5f7f847c474293e Rafael Ramos <<EMAIL>> 1749648587 +0800	commit: booking
98bd904c85997d9f76abfaaea5f7f847c474293e 50fdafd77a9dd7fecde10fb09d012f83a0229923 Rafael Ramos <<EMAIL>> 1749648772 +0800	checkout: moving from main to dashboard
50fdafd77a9dd7fecde10fb09d012f83a0229923 98bd904c85997d9f76abfaaea5f7f847c474293e Rafael Ramos <<EMAIL>> 1749648775 +0800	merge main: Fast-forward
98bd904c85997d9f76abfaaea5f7f847c474293e 98bd904c85997d9f76abfaaea5f7f847c474293e Rafael Ramos <<EMAIL>> 1749689365 +0800	checkout: moving from dashboard to main
98bd904c85997d9f76abfaaea5f7f847c474293e 483557d43f7beba475eef7f79dffa8d16a7eb930 Rafael Ramos <<EMAIL>> 1749689398 +0800	pull origin main: Fast-forward
483557d43f7beba475eef7f79dffa8d16a7eb930 98bd904c85997d9f76abfaaea5f7f847c474293e Rafael Ramos <<EMAIL>> 1749689559 +0800	checkout: moving from main to dashboard
98bd904c85997d9f76abfaaea5f7f847c474293e 483557d43f7beba475eef7f79dffa8d16a7eb930 Rafael Ramos <<EMAIL>> 1749689564 +0800	merge main: Fast-forward
483557d43f7beba475eef7f79dffa8d16a7eb930 483557d43f7beba475eef7f79dffa8d16a7eb930 Rafael Ramos <<EMAIL>> 1749695549 +0800	reset: moving to HEAD
483557d43f7beba475eef7f79dffa8d16a7eb930 483557d43f7beba475eef7f79dffa8d16a7eb930 Rafael Ramos <<EMAIL>> 1749695560 +0800	checkout: moving from dashboard to main
483557d43f7beba475eef7f79dffa8d16a7eb930 90f2cf8d28686415625ddc258daddb44bd874218 Rafael Ramos <<EMAIL>> 1749731983 +0800	pull origin main: Fast-forward
90f2cf8d28686415625ddc258daddb44bd874218 483557d43f7beba475eef7f79dffa8d16a7eb930 Rafael Ramos <<EMAIL>> 1749732015 +0800	checkout: moving from main to dashboard
483557d43f7beba475eef7f79dffa8d16a7eb930 90f2cf8d28686415625ddc258daddb44bd874218 Rafael Ramos <<EMAIL>> 1749732033 +0800	merge main: Fast-forward
90f2cf8d28686415625ddc258daddb44bd874218 2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 Rafael Ramos <<EMAIL>> 1749734676 +0800	commit: not fix the error
2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749734705 +0800	checkout: moving from dashboard to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 Rafael Ramos <<EMAIL>> 1749734805 +0800	checkout: moving from bug to dashboard
2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 Rafael Ramos <<EMAIL>> 1749735561 +0800	reset: moving to HEAD
2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 90f2cf8d28686415625ddc258daddb44bd874218 Rafael Ramos <<EMAIL>> 1749735584 +0800	checkout: moving from dashboard to main
90f2cf8d28686415625ddc258daddb44bd874218 2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 Rafael Ramos <<EMAIL>> 1749737439 +0800	checkout: moving from main to dashboard
2d8be76d8091bb1f48a6c00a807d9eef55a6d5a9 946acf59281b99f9d7e708f2aeb516ca6bf892f3 Rafael Ramos <<EMAIL>> 1749741796 +0800	commit: bahala na
946acf59281b99f9d7e708f2aeb516ca6bf892f3 90f2cf8d28686415625ddc258daddb44bd874218 Rafael Ramos <<EMAIL>> 1749741812 +0800	checkout: moving from dashboard to main
90f2cf8d28686415625ddc258daddb44bd874218 3ce9b59f0685adbdf6e85ee047665da101f9f935 Rafael Ramos <<EMAIL>> 1749742570 +0800	commit: minor
3ce9b59f0685adbdf6e85ee047665da101f9f935 946acf59281b99f9d7e708f2aeb516ca6bf892f3 Rafael Ramos <<EMAIL>> 1749743708 +0800	checkout: moving from main to dashboard
946acf59281b99f9d7e708f2aeb516ca6bf892f3 22c6dc9ca07b77555b07d0c0e5a086f71c668a43 Rafael Ramos <<EMAIL>> 1749743908 +0800	commit: req
22c6dc9ca07b77555b07d0c0e5a086f71c668a43 22c6dc9ca07b77555b07d0c0e5a086f71c668a43 Rafael Ramos <<EMAIL>> 1749746825 +0800	reset: moving to HEAD
22c6dc9ca07b77555b07d0c0e5a086f71c668a43 3ce9b59f0685adbdf6e85ee047665da101f9f935 Rafael Ramos <<EMAIL>> 1749746908 +0800	checkout: moving from dashboard to main
3ce9b59f0685adbdf6e85ee047665da101f9f935 b6c85a3d7f7e49bc7b86dea183eca0a152883f28 Rafael Ramos <<EMAIL>> 1749774012 +0800	commit: docs
b6c85a3d7f7e49bc7b86dea183eca0a152883f28 b6c85a3d7f7e49bc7b86dea183eca0a152883f28 Rafael Ramos <<EMAIL>> 1749774133 +0800	checkout: moving from main to checking
b6c85a3d7f7e49bc7b86dea183eca0a152883f28 58ec7a648aefefd826b8f64da30fe19c6c0e3f66 Rafael Ramos <<EMAIL>> 1749774815 +0800	commit: book
58ec7a648aefefd826b8f64da30fe19c6c0e3f66 3303f1dad6281a727024f2bcd9c71ffab202dbb3 Rafael Ramos <<EMAIL>> 1749775291 +0800	commit: minor
3303f1dad6281a727024f2bcd9c71ffab202dbb3 b6c85a3d7f7e49bc7b86dea183eca0a152883f28 Rafael Ramos <<EMAIL>> 1749775299 +0800	checkout: moving from checking to main
b6c85a3d7f7e49bc7b86dea183eca0a152883f28 63edb28d8ae05ec70030df9411b5930d1a8ad649 Rafael Ramos <<EMAIL>> 1749964183 +0800	commit: minor changes
63edb28d8ae05ec70030df9411b5930d1a8ad649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749964221 +0800	checkout: moving from main to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749964324 +0800	checkout: moving from bug to revert
0c99dc19a865f3d0831a5b0b231d42a86a21a649 63edb28d8ae05ec70030df9411b5930d1a8ad649 Rafael Ramos <<EMAIL>> 1749964333 +0800	checkout: moving from revert to main
63edb28d8ae05ec70030df9411b5930d1a8ad649 63edb28d8ae05ec70030df9411b5930d1a8ad649 Rafael Ramos <<EMAIL>> 1749964346 +0800	checkout: moving from main to main
63edb28d8ae05ec70030df9411b5930d1a8ad649 63edb28d8ae05ec70030df9411b5930d1a8ad649 Rafael Ramos <<EMAIL>> 1749964353 +0800	checkout: moving from main to revert
63edb28d8ae05ec70030df9411b5930d1a8ad649 63edb28d8ae05ec70030df9411b5930d1a8ad649 Rafael Ramos <<EMAIL>> 1749964919 +0800	checkout: moving from revert to main
63edb28d8ae05ec70030df9411b5930d1a8ad649 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749965011 +0800	revert: Revert "booking"
8838a66cb19f06553937823cc6369943bb91e0ff 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749965616 +0800	checkout: moving from main to new
8838a66cb19f06553937823cc6369943bb91e0ff 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749968654 +0800	reset: moving to HEAD
8838a66cb19f06553937823cc6369943bb91e0ff 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749969028 +0800	checkout: moving from new to main
8838a66cb19f06553937823cc6369943bb91e0ff 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749970376 +0800	checkout: moving from main to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749970583 +0800	checkout: moving from bug to new
8838a66cb19f06553937823cc6369943bb91e0ff 0c99dc19a865f3d0831a5b0b231d42a86a21a649 Rafael Ramos <<EMAIL>> 1749970687 +0800	checkout: moving from new to bug
0c99dc19a865f3d0831a5b0b231d42a86a21a649 b43f905004d5e74e63fbb25459ab00a06e6dfb84 Rafael Ramos <<EMAIL>> 1749973251 +0800	commit: n/a
b43f905004d5e74e63fbb25459ab00a06e6dfb84 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749973254 +0800	checkout: moving from bug to main
8838a66cb19f06553937823cc6369943bb91e0ff 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749973338 +0800	checkout: moving from main to new
8838a66cb19f06553937823cc6369943bb91e0ff 177755b27314f5e4883848a95fa0d443304362b3 Rafael Ramos <<EMAIL>> 1749975328 +0800	commit: minor
177755b27314f5e4883848a95fa0d443304362b3 b43f905004d5e74e63fbb25459ab00a06e6dfb84 Rafael Ramos <<EMAIL>> 1749975342 +0800	checkout: moving from new to bug
b43f905004d5e74e63fbb25459ab00a06e6dfb84 177755b27314f5e4883848a95fa0d443304362b3 Rafael Ramos <<EMAIL>> 1749975506 +0800	checkout: moving from bug to new
177755b27314f5e4883848a95fa0d443304362b3 177755b27314f5e4883848a95fa0d443304362b3 Rafael Ramos <<EMAIL>> 1749981793 +0800	reset: moving to HEAD
177755b27314f5e4883848a95fa0d443304362b3 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749983904 +0800	commit: filter
7ea88098dbd566ec6056d7c8811290882c2c018e 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749984805 +0800	checkout: moving from new to main
8838a66cb19f06553937823cc6369943bb91e0ff 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749985104 +0800	checkout: moving from main to new
7ea88098dbd566ec6056d7c8811290882c2c018e 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749985130 +0800	checkout: moving from new to attachment
7ea88098dbd566ec6056d7c8811290882c2c018e 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749988160 +0800	reset: moving to HEAD
7ea88098dbd566ec6056d7c8811290882c2c018e 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749988295 +0800	checkout: moving from attachment to new
7ea88098dbd566ec6056d7c8811290882c2c018e 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749988339 +0800	checkout: moving from new to main
8838a66cb19f06553937823cc6369943bb91e0ff 8838a66cb19f06553937823cc6369943bb91e0ff Rafael Ramos <<EMAIL>> 1749988373 +0800	checkout: moving from main to main2
8838a66cb19f06553937823cc6369943bb91e0ff 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749988414 +0800	checkout: moving from main2 to new
7ea88098dbd566ec6056d7c8811290882c2c018e 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749988440 +0800	checkout: moving from new to new2
7ea88098dbd566ec6056d7c8811290882c2c018e 7ea88098dbd566ec6056d7c8811290882c2c018e Rafael Ramos <<EMAIL>> 1749994135 +0800	reset: moving to HEAD
7ea88098dbd566ec6056d7c8811290882c2c018e 22c6dc9ca07b77555b07d0c0e5a086f71c668a43 Rafael Ramos <<EMAIL>> 1749997703 +0800	checkout: moving from new2 to dashboard
22c6dc9ca07b77555b07d0c0e5a086f71c668a43 720922e046f4fc9d7c42496667c580cfcb5a0d89 Rafael Ramos <<EMAIL>> 1749999391 +0800	commit: minor CHANGES
720922e046f4fc9d7c42496667c580cfcb5a0d89 03c520aff90eaa7d0dcc18ca9722d0635f3d3ece Rafael Ramos <<EMAIL>> 1750000256 +0800	commit: dashboard
03c520aff90eaa7d0dcc18ca9722d0635f3d3ece 720922e046f4fc9d7c42496667c580cfcb5a0d89 Rafael Ramos <<EMAIL>> 1750016521 +0800	reset: moving to 720922e
720922e046f4fc9d7c42496667c580cfcb5a0d89 720922e046f4fc9d7c42496667c580cfcb5a0d89 Rafael Ramos <<EMAIL>> 1750018691 +0800	checkout: moving from dashboard to update
720922e046f4fc9d7c42496667c580cfcb5a0d89 c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750023213 +0800	commit: profile pic bugs
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf 720922e046f4fc9d7c42496667c580cfcb5a0d89 Rafael Ramos <<EMAIL>> 1750027562 +0800	checkout: moving from update to dashboard
720922e046f4fc9d7c42496667c580cfcb5a0d89 c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750027565 +0800	merge update: Fast-forward
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750029297 +0800	checkout: moving from dashboard to update
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750087964 +0800	checkout: moving from update to dashboard
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750087981 +0800	checkout: moving from dashboard to update
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750121779 +0800	commit: ISPMxAPPDEV
319bdeda5179c588ebf2c9f265ad34d064b94daf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750121799 +0800	checkout: moving from update to test
319bdeda5179c588ebf2c9f265ad34d064b94daf c9bad1c1020849289a6fe3a3e6ec85de0a3427bf Rafael Ramos <<EMAIL>> 1750135574 +0800	checkout: moving from test to dashboard
c9bad1c1020849289a6fe3a3e6ec85de0a3427bf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750135578 +0800	merge update: Fast-forward
319bdeda5179c588ebf2c9f265ad34d064b94daf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750135600 +0800	checkout: moving from dashboard to update
319bdeda5179c588ebf2c9f265ad34d064b94daf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750135613 +0800	checkout: moving from update to dashboard
319bdeda5179c588ebf2c9f265ad34d064b94daf 319bdeda5179c588ebf2c9f265ad34d064b94daf Rafael Ramos <<EMAIL>> 1750136821 +0800	checkout: moving from dashboard to test
