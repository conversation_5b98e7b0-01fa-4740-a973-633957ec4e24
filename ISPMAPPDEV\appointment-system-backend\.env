MONGODB_URI=mongodb+srv://rodolfojrxgt:<EMAIL>/?retryWrites=true&w=majority&appName=AppointEase
JWT_SECRET=de2763cc281668e6d1b79cbabc0438167cecee0deb8451d4fbbc4f08045d3727a06f07db91ef330e3ec02753e47e07ec777fdf97ed360858b849f838cdbac1a2
GMAIL_USER=raf<PERSON><PERSON><PERSON>@student.laverdad.edu.ph
GMAIL_PASS=xpvsvgrcenaoqldn
NODE_ENV=production
PORT=5000

CLOUDINARY_CLOUD_NAME=dp9hjzio8
CLOUDINARY_API_KEY=549258433219286
CLOUDINARY_API_SECRET=Y3YY_zxpOva4LZI6jkC2qWLP-R8


# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173


# GOOGLE_CLIENT_ID=976959476988-h1e1m59q0ipqcov99rcjd1aqvkhahvaj.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=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
# GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback

# # mongodb+srv://LVAppointEase:<EMAIL>/?retryWrites=true&w=majority&appName=AppointEase

# Environment variables
.env

# Dependencies
node_modules/

# Logs
logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock