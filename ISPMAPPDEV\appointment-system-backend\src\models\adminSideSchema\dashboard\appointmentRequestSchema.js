const mongoose = require("mongoose");

const AppointmentRequestSchema = new mongoose.Schema({
  transactionNumber: {
    type: String,
    required: true,
    unique: true,
  },
  name: String,
  lastSYAttended: String,
  programGradeStrand: String,
  contactNo: String,
  emailAddress: String,
  // Store attachment references to the Attachment collection
  attachmentProof: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Attachment",
    default: null,
  },
  // Keep the old string field for backward compatibility during migration
  attachmentProofLegacy: String,
  requestType: String,
  dateOfRequest: Date,
  claimingMethod: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model("AppointmentRequest", AppointmentRequestSchema);
