[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/Grraffic/appointment-system-backend.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "bug"]
	vscode-merge-base = origin/main
[branch "dashboard"]
	vscode-merge-base = origin/main
[branch "new"]
	vscode-merge-base = origin/main
[branch "update"]
	vscode-merge-base = origin/main
[branch "test"]
	vscode-merge-base = origin/main
