const express = require("express");
const router = express.Router();
const {
  getAppointmentRequestsWithAttachments,
  getAppointmentRequestByTransaction,
} = require("../../../controllers/adminSideController/dashboardController/appointmentRequest.controller");

// Get all appointment requests with attachments
router.get("/", getAppointmentRequestsWithAttachments);

// Get single appointment request by transaction number
router.get("/:transactionNumber", getAppointmentRequestByTransaction);

module.exports = router;
